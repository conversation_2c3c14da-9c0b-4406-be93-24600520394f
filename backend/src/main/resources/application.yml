# Database Configuration - Using PostgreSQL with HikariCP for Multitenant
spring:
  datasource:
    url: ****************************************
    username: postgres
    password: password
    driver-class-name: org.postgresql.Driver
    hikari:
      connection-timeout: 20000
      minimum-idle: 5
      maximum-pool-size: 15
      idle-timeout: 300000
      max-lifetime: 1200000
      auto-commit: true
  jpa:
    properties:
      hibernate:
        format_sql: false
        default_schema: public
        # Database-agnostic configuration
        dialect: org.hibernate.dialect.PostgreSQLDialect
        # Ensure proper type handling
        type:
          preferred_instant_jdbc_type: TIMESTAMP
        # Enable second-level cache
        cache:
          use_second_level_cache: false
          use_query_cache: false
    hibernate:
      ddl-auto: update
    show-sql: false
    # Database-agnostic settings
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    open-in-view: false
  flyway:
    enabled: false  # Disabled - using custom tenant migration service
    schemas: public  # Master schema for tenant management
    baseline-on-migrate: true
    locations: classpath:db/migration
  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=10m
  # Async Configuration
  task:
    execution:
      pool:
        core-size: 5
        max-size: 20
        queue-capacity: 100
    scheduling:
      pool:
        size: 5

# JWT Configuration
app:
  jwt:
    secret: 9a4f2c8d3b7a1e6f5d4c2b8a7e5f3d2c1b8a7e5f3d2c1b8a7e5f3d2c1b8a7e5f
    expiration-ms: 86400000
  scheduling:
    enabled: true
  sla:
    monitoring:
      enabled: true
      check-interval: 900000  # 15 minutes
    escalation:
      enabled: true
  performance:
    metrics:
      enabled: true
      retention-days: 30

# AI Assistance Configuration
ai:
  assist:
    enabled: true
    openai:
      api-key: ${OPENAI_API_KEY:********************************************************************************************************************************************************************}
      model: gpt-4.1-mini-2025-04-14
      max-tokens: 1000
      temperature: 0.7
    nlp:
      enabled: true
      fallback-enabled: true

# Multitenant Configuration
multitenant:
  master:
    schema: public
  tenant:
    schema-prefix: tenant_
    default-max-users: 100
    default-max-storage-mb: 1000
  interceptor:
    enabled: true
    excluded-paths:
      - /api/tenants/register
      - /api/tenant-auth/**
      - /actuator/**
      - /health
      - /metrics

# Server Configuration
server:
  port: 8443
  servlet:
    context-path: /
  ssl:
    enabled: true
    key-store: classpath:certs/murthy.jks
    key-store-password: changeit
    key-store-type: JKS
    key-alias: murthy
    key-password: changeit
    protocol: TLS
    enabled-protocols: TLSv1.2,TLSv1.3
  compression:
    enabled: true
  error:
    include-message: always
    include-binding-errors: always

# Production Features
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  prometheus:
    metrics:
      export:
        enabled: true

# Additional Spring Configuration moved to main spring section above

# Logging Configuration
logging:
  level:
    root: INFO
    '[com.bugtracker]': DEBUG
    '[org.springframework]': INFO
    '[org.hibernate]': INFO
  file:
    name: ./logs/bug-tracker.log
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  logback:
    rollingpolicy:
      file-name-pattern: ./logs/bug-tracker-%d{yyyy-MM-dd}.%i.log
      max-file-size: 10MB
      max-history: 30
      total-size-cap: 3GB